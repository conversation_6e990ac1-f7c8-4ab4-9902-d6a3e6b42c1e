<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShopHub - Shop</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;900&display=swap" rel="stylesheet">
</head>

<body>
    <div class="super_container">
        <!-- Header -->
        <header class="header">
            <!-- Top Navigation -->
            <div class="top_nav">
                <div class="container">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="top_nav_left">free shipping on all orders over $50</div>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="top_nav_right">
                                <ul class="top_nav_menu">
                                    <li class="currency">
                                        <a href="#">USD <i class="fa fa-angle-down"></i></a>
                                    </li>
                                    <li class="language">
                                        <a href="#">English <i class="fa fa-angle-down"></i></a>
                                    </li>
                                    <li class="account">
                                        <a href="#">My Account <i class="fa fa-angle-down"></i></a>
                                        <ul class="account_selection">
                                            <li><a href="profile.html"><i class="fa fa-user"></i>Profile</a></li>
                                            <li><a href="index.html"><i class="fa fa-sign-out"></i>Logout</a></li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Navigation -->
            <div class="main_nav_container">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="logo_container">
                                <a href="index.html">Shop<span>Hub</span></a>
                            </div>
                            <nav class="navbar">
                                <ul class="navbar_menu">
                                    <li><a href="index.html">home</a></li>
                                    <li><a href="#" class="active">shop</a></li>
                                    <li><a href="#">promotion</a></li>
                                    <li><a href="#">categories</a></li>
                                    <li><a href="#">blog</a></li>
                                    <li><a href="#">contact</a></li>
                                </ul>
                                <ul class="navbar_user">
                                    <li><a href="#"><i class="fa fa-search"></i></a></li>
                                    <li><a href="profile.html"><i class="fa fa-user"></i></a></li>
                                    <li class="checkout">
                                        <a href="#" onclick="toggleCart()">
                                            <i class="fa fa-shopping-cart"></i>
                                            <span class="checkout_items" id="cart-count">0</span>
                                        </a>
                                    </li>
                                </ul>
                                <div class="hamburger_container">
                                    <i class="fa fa-bars"></i>
                                </div>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Shop Page Content -->
        <div class="shop_section">
            <div class="container">
                <div class="row">
                    <div class="col text-center">
                        <div class="section_title">
                            <h2>Featured Products</h2>
                        </div>
                    </div>
                </div>

                <!-- Products Filter -->
                <div class="row align-items-center">
                    <div class="col text-center">
                        <div class="new_arrivals_sorting">
                            <ul class="arrivals_grid_sorting clearfix button-group filters-button-group">
                                <li class="grid_sorting_button button active" data-filter="*">all</li>
                                <li class="grid_sorting_button button" data-filter=".women">women's</li>
                                <li class="grid_sorting_button button" data-filter=".accessories">accessories</li>
                                <li class="grid_sorting_button button" data-filter=".men">men's</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="row">
                    <div class="col">
                        <div class="product-grid" id="product-grid">
                            <!-- Products will be loaded here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shopping Cart Sidebar -->
        <div class="cart-sidebar" id="cart-sidebar">
            <div class="cart-header">
                <h3>Shopping Cart</h3>
                <button class="close-cart" onclick="toggleCart()">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="cart-content">
                <div class="cart-items" id="cart-items">
                    <!-- Cart items will be added here -->
                </div>
                <div class="cart-total">
                    <div class="total-row">
                        <span>Total: $<span id="cart-total">0.00</span></span>
                    </div>
                    <button class="checkout-btn">
                        <i class="fa fa-credit-card"></i>
                        Checkout
                    </button>
                </div>
            </div>
        </div>

        <!-- Cart Overlay -->
        <div class="cart-overlay" id="cart-overlay" onclick="toggleCart()"></div>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="footer_nav_container d-flex">
                            <div class="footer_nav">
                                <div class="footer_nav_title">Categories</div>
                                <ul class="footer_nav_ul">
                                    <li><a href="#">Men</a></li>
                                    <li><a href="#">Women</a></li>
                                    <li><a href="#">Accessories</a></li>
                                    <li><a href="#">Shoes</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="footer_social d-flex flex-row align-items-center justify-content-lg-end justify-content-center">
                            <ul class="footer_social_list">
                                <li class="footer_social_item"><a href="#"><i class="fab fa-facebook"></i></a></li>
                                <li class="footer_social_item"><a href="#"><i class="fab fa-twitter"></i></a></li>
                                <li class="footer_social_item"><a href="#"><i class="fab fa-instagram"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="footer_nav_container">
                            <div class="cr">©2024 All Rights Reserved. ShopHub | Designed by <a href="#">ShopHub Team</a></div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="main.js"></script>
</body>
</html>
            <div class="profile-avatar" onclick="toggleProfileMenu()">
              <i class="fas fa-user"></i>
            </div>
            <div class="dropdown-menu" id="profile-menu">
              <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
              <a href="#"><i class="fas fa-box"></i> Orders</a>
              <a href="#"><i class="fas fa-cog"></i> Settings</a>
              <a href="#" onclick="logout()"
                ><i class="fas fa-sign-out-alt"></i> Logout</a
              >
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">Welcome to ShopHub</h1>
          <p class="hero-subtitle">
            Discover amazing products at unbeatable prices
          </p>
          <button class="hero-btn">Shop Now</button>
        </div>
        <div class="hero-image">
          <div class="hero-graphic">
            <i class="fas fa-shopping-cart"></i>
          </div>
        </div>
      </section>

      <!-- Categories -->
      <section class="categories-section">
        <h2 class="section-title">Shop by Category</h2>
        <div class="categories-grid">
          <div class="category-card" onclick="filterByCategory('electronics')">
            <i class="fas fa-laptop"></i>
            <h3>Electronics</h3>
          </div>
          <div class="category-card" onclick="filterByCategory('fashion')">
            <i class="fas fa-tshirt"></i>
            <h3>Fashion</h3>
          </div>
          <div class="category-card" onclick="filterByCategory('home')">
            <i class="fas fa-home"></i>
            <h3>Home & Garden</h3>
          </div>
          <div class="category-card" onclick="filterByCategory('sports')">
            <i class="fas fa-dumbbell"></i>
            <h3>Sports</h3>
          </div>
          <div class="category-card" onclick="filterByCategory('books')">
            <i class="fas fa-book"></i>
            <h3>Books</h3>
          </div>
          <div class="category-card" onclick="filterByCategory('beauty')">
            <i class="fas fa-palette"></i>
            <h3>Beauty</h3>
          </div>
        </div>
      </section>

      <!-- Filters and Products -->
      <section class="products-section">
        <div class="products-header">
          <h2 class="section-title">Featured Products</h2>
          <div class="filters">
            <select id="sort-select" onchange="sortProducts()">
              <option value="featured">Featured</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
              <option value="newest">Newest</option>
            </select>
            <div class="view-toggle">
              <button class="view-btn active" data-view="grid">
                <i class="fas fa-th-large"></i>
              </button>
              <button class="view-btn" data-view="list">
                <i class="fas fa-list"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="products-grid" id="products-container">
          <!-- Products will be dynamically loaded here -->
        </div>

        <div class="loading-spinner" id="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading products...</p>
        </div>
      </section>
    </main>

    <!-- Shopping Cart Sidebar -->
    <div class="cart-sidebar" id="cart-sidebar">
      <div class="cart-header">
        <h3>Shopping Cart</h3>
        <button class="close-cart" onclick="toggleCart()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="cart-items" id="cart-items">
        <div class="empty-cart">
          <i class="fas fa-shopping-cart"></i>
          <p>Your cart is empty</p>
          <p class="empty-cart-subtitle">Add some products to get started</p>
        </div>
      </div>
      <div class="cart-footer">
        <div class="cart-total">
          <span>Total: $<span id="cart-total">0.00</span></span>
        </div>
        <button class="checkout-btn" onclick="checkout()">
          <i class="fas fa-credit-card"></i>
          Checkout
        </button>
      </div>
    </div>

    <!-- Product Modal -->
    <div class="modal-overlay" id="product-modal">
      <div class="modal-content">
        <button class="close-modal" onclick="closeProductModal()">
          <i class="fas fa-times"></i>
        </button>
        <div class="modal-body" id="modal-body">
          <!-- Product details will be loaded here -->
        </div>
      </div>
    </div>

    <script src="main.js"></script>
  </body>
</html>
