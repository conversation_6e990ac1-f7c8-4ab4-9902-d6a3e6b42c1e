/* ==================== GLOBAL STYLES ==================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 23px;
  font-weight: 400;
  color: #1e1e27;
  background: #ffffff;
}

div {
  display: block;
  position: relative;
  box-sizing: border-box;
}

ul {
  list-style: none;
  margin-bottom: 0px;
}

p {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  line-height: 1.7;
  font-weight: 500;
  color: #989898;
}

a,
a:hover,
a:visited,
a:active,
a:link {
  text-decoration: none;
  color: inherit;
}

a:hover {
  color: #fe4c50;
}

/* ==================== LAYOUT CLASSES ==================== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col,
.col-md-4,
.col-md-6,
.col-lg-6,
.col-lg-12 {
  padding: 0 15px;
  flex: 1;
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-lg-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

.col-lg-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.d-flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-lg-end {
  justify-content: flex-end;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.fill_height {
  height: 100%;
}

/* ==================== HEADER STYLES ==================== */
.super_container {
  width: 100%;
  overflow: hidden;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  z-index: 100;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  transition: all 300ms ease;
}

/* Top Navigation */
.top_nav {
  width: 100%;
  background: #1e1e27;
  height: 40px;
}

.top_nav_left {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  line-height: 40px;
  text-transform: uppercase;
}

.top_nav_right {
  line-height: 40px;
}

.top_nav_menu {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  list-style: none;
  margin: 0;
}

.top_nav_menu li {
  position: relative;
  margin-left: 30px;
}

.top_nav_menu li a {
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  text-transform: uppercase;
  transition: all 200ms ease;
}

.top_nav_menu li a:hover {
  color: #fe4c50;
}

.account_selection {
  position: absolute;
  top: 100%;
  right: 0;
  width: 180px;
  background: #ffffff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 300ms ease;
  z-index: 1000;
}

.account:hover .account_selection {
  opacity: 1;
  visibility: visible;
}

.account_selection li {
  margin: 0;
}

.account_selection li a {
  display: block;
  font-size: 13px;
  font-weight: 400;
  color: #1e1e27;
  padding: 10px 15px;
  border-bottom: 1px solid #f5f5f5;
  text-transform: none;
}

.account_selection li a:hover {
  background: #fe4c50;
  color: #ffffff;
}

/* Main Navigation */
.main_nav_container {
  width: 100%;
  background: #ffffff;
  padding: 20px 0;
}

.logo_container {
  float: left;
  margin-right: 50px;
}

.logo_container a {
  font-family: "Poppins", sans-serif;
  font-size: 30px;
  font-weight: 900;
  color: #1e1e27;
  text-transform: lowercase;
}

.logo_container a span {
  color: #fe4c50;
}

.navbar {
  float: right;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.navbar_menu {
  display: flex;
  flex-direction: row;
  align-items: center;
  list-style: none;
  margin: 0 50px 0 0;
}

.navbar_menu li {
  margin-right: 45px;
}

.navbar_menu li:last-child {
  margin-right: 0;
}

.navbar_menu li a {
  font-size: 14px;
  font-weight: 500;
  color: #1e1e27;
  text-transform: uppercase;
  transition: all 200ms ease;
}

.navbar_menu li a:hover {
  color: #fe4c50;
}

.navbar_user {
  display: flex;
  flex-direction: row;
  align-items: center;
  list-style: none;
  margin: 0;
}

.navbar_user li {
  margin-left: 25px;
}

.navbar_user li a {
  font-size: 16px;
  color: #1e1e27;
  transition: all 200ms ease;
}

.navbar_user li a:hover {
  color: #fe4c50;
}

.checkout {
  position: relative;
}

.checkout_items {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #fe4c50;
  border-radius: 50%;
  color: #ffffff;
  font-size: 11px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
}

.hamburger_container {
  display: none;
  cursor: pointer;
}

.hamburger_container i {
  font-size: 18px;
  color: #1e1e27;
}

/* ==================== MAIN SLIDER ==================== */
.main_slider {
  width: 100%;
  height: 670px;
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
    url("https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80")
      center/cover;
  margin-top: 111px;
  display: flex;
  align-items: center;
}

.main_slider_content {
  z-index: 10;
}

.main_slider_content h6 {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  margin-bottom: 15px;
}

.main_slider_content h1 {
  font-size: 72px;
  font-weight: 300;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 35px;
}

/* Red Button */
.red_button {
  display: inline-block;
  background: #fe4c50;
  border-radius: 3px;
  height: 50px;
  transition: all 200ms ease;
}

.red_button:hover {
  background: #1e1e27;
}

.red_button a {
  display: block;
  font-size: 14px;
  font-weight: 500;
  line-height: 50px;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  padding-left: 35px;
  padding-right: 35px;
}

/* ==================== BANNER ==================== */
.banner {
  width: 100%;
  margin-top: 100px;
  margin-bottom: 100px;
}

.banner_item {
  width: 100%;
  height: 240px;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  transition: all 200ms ease;
  cursor: pointer;
}

.banner_item:hover {
  transform: translateY(-5px);
}

.banner_category a {
  font-size: 22px;
  font-weight: 600;
  color: #1e1e27;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  transition: all 200ms ease;
}

.banner_category a:hover {
  color: #fe4c50;
}

/* ==================== NEW ARRIVALS ==================== */
.new_arrivals {
  width: 100%;
  padding-bottom: 120px;
}

.section_title {
  text-align: center;
  margin-bottom: 50px;
}

.section_title h2 {
  font-size: 40px;
  font-weight: 400;
  color: #1e1e27;
  margin-bottom: 15px;
}

.new_arrivals_sorting {
  margin-bottom: 70px;
}

.arrivals_grid_sorting {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  list-style: none;
  margin: 0;
}

.grid_sorting_button {
  font-size: 14px;
  font-weight: 400;
  color: #b5b5b5;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  cursor: pointer;
  margin: 0 30px;
  transition: all 200ms ease;
}

.grid_sorting_button:hover,
.grid_sorting_button.active {
  color: #fe4c50;
}

/* ==================== FOOTER ==================== */
.footer {
  width: 100%;
  background: #1e1e27;
  padding: 50px 0;
}

.footer_nav_container {
  margin-bottom: 30px;
}

.footer_nav {
  margin-right: 50px;
}

.footer_nav_title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  margin-bottom: 20px;
}

.footer_nav_ul {
  list-style: none;
  margin: 0;
}

.footer_nav_ul li {
  margin-bottom: 10px;
}

.footer_nav_ul li a {
  font-size: 14px;
  color: #b5b5b5;
  transition: all 200ms ease;
}

.footer_nav_ul li a:hover {
  color: #fe4c50;
}

.footer_social_list {
  display: flex;
  flex-direction: row;
  align-items: center;
  list-style: none;
  margin: 0;
}

.footer_social_item {
  margin-left: 20px;
}

.footer_social_item a {
  display: block;
  width: 40px;
  height: 40px;
  background: #fe4c50;
  border-radius: 50%;
  color: #ffffff;
  text-align: center;
  line-height: 40px;
  transition: all 200ms ease;
}

.footer_social_item a:hover {
  background: #ffffff;
  color: #fe4c50;
}

.cr {
  font-size: 14px;
  color: #b5b5b5;
  text-align: center;
  margin-top: 30px;
}

/* ==================== SHOP SECTION ==================== */
.shop_section {
  margin-top: 150px;
  padding: 50px 0 100px 0;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.product-item {
  background: #ffffff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 300ms ease;
  cursor: pointer;
}

.product-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.product_image {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.product_image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 300ms ease;
}

.product-item:hover .product_image img {
  transform: scale(1.1);
}

.product_info {
  padding: 20px;
}

.product_name {
  font-size: 16px;
  font-weight: 500;
  color: #1e1e27;
  margin-bottom: 10px;
}

.product_name a {
  color: inherit;
  transition: all 200ms ease;
}

.product_name a:hover {
  color: #fe4c50;
}

.product_price {
  font-size: 18px;
  font-weight: 600;
  color: #fe4c50;
}

.product_price span {
  font-size: 14px;
  color: #b5b5b5;
  text-decoration: line-through;
  margin-left: 10px;
}

.product_bubble {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #fe4c50;
  color: #ffffff;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

.add-to-cart-btn {
  width: 100%;
  background: #fe4c50;
  color: #ffffff;
  border: none;
  padding: 12px;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 200ms ease;
  margin-top: 15px;
}

.add-to-cart-btn:hover {
  background: #1e1e27;
}

/* ==================== CART SIDEBAR ==================== */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: #ffffff;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 300ms ease;
  display: flex;
  flex-direction: column;
}

.cart-sidebar.active {
  right: 0;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.cart-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e1e27;
  margin: 0;
}

.close-cart {
  background: none;
  border: none;
  font-size: 18px;
  color: #1e1e27;
  cursor: pointer;
  transition: color 200ms ease;
}

.close-cart:hover {
  color: #fe4c50;
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cart-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 5px;
  margin-right: 15px;
}

.cart-item-info {
  flex: 1;
}

.cart-item-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e1e27;
  margin-bottom: 5px;
}

.cart-item-price {
  font-size: 14px;
  color: #fe4c50;
  font-weight: 600;
}

.cart-total {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.total-row {
  font-size: 18px;
  font-weight: 600;
  color: #1e1e27;
  margin-bottom: 20px;
}

.checkout-btn {
  width: 100%;
  background: #fe4c50;
  color: #ffffff;
  border: none;
  padding: 15px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 200ms ease;
}

.checkout-btn:hover {
  background: #1e1e27;
}

.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 300ms ease;
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* ==================== RESPONSIVE DESIGN ==================== */
@media (max-width: 768px) {
  .hamburger_container {
    display: block;
  }

  .navbar_menu,
  .navbar_user {
    display: none;
  }

  .logo_container {
    float: none;
    text-align: center;
    margin: 0;
  }

  .main_slider_content h1 {
    font-size: 48px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .cart-sidebar {
    width: 100%;
    right: -100%;
  }
}

/* ==================== PROFILE SECTION ==================== */
.profile_section {
  margin-top: 150px;
  padding: 50px 0 100px 0;
}

.profile_container {
  max-width: 800px;
  margin: 0 auto;
}

.profile_header {
  display: flex;
  align-items: center;
  margin-bottom: 50px;
  padding: 30px;
  background: #f8f8f8;
  border-radius: 10px;
}

.profile_avatar {
  margin-right: 30px;
}

.profile_avatar i {
  font-size: 80px;
  color: #fe4c50;
}

.profile_info h2 {
  font-size: 32px;
  font-weight: 600;
  color: #1e1e27;
  margin-bottom: 10px;
}

.profile_info p {
  font-size: 16px;
  color: #989898;
}

.profile_actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.action_card {
  background: #ffffff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 300ms ease;
}

.action_card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.action_card i {
  font-size: 48px;
  color: #fe4c50;
  margin-bottom: 20px;
}

.action_card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e1e27;
  margin-bottom: 10px;
}

.action_card p {
  font-size: 14px;
  color: #989898;
  margin-bottom: 20px;
}

.action_card .red_button {
  display: inline-block;
  margin-top: 10px;
}

/* ==================== AUTH PAGES STYLES ==================== */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fe4c50 0%, #fde0db 50%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.auth-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 520px;
  padding: 2rem;
}

.auth-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(200, 200, 200, 0.3);
  animation: slideUp 0.6s ease-out;
}

.register-card {
  max-width: 440px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.logo i {
  font-size: 2rem;
  color: #fe4c50;
}

.logo h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1e1e27;
}

.logo h1 span {
  color: #fe4c50;
}

.auth-subtitle {
  color: #989898;
  font-size: 0.925rem;
  font-weight: 400;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  position: relative;
}

.input-group input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #fde0db;
  border-radius: 12px;
  font-size: 0.925rem;
  transition: all 0.3s ease;
  background: #fff;
  font-family: "Poppins", sans-serif;
}

.input-group input:focus {
  outline: none;
  border-color: #fe4c50;
  box-shadow: 0 0 0 3px rgba(254, 76, 80, 0.1);
}

.input-group label {
  position: absolute;
  left: 3rem;
  top: 1rem;
  color: #989898;
  font-size: 0.875rem;
  pointer-events: none;
  transition: all 0.3s ease;
}

.input-group input:focus + label,
.input-group input:not(:placeholder-shown) + label {
  top: -0.5rem;
  left: 1rem;
  font-size: 0.75rem;
  color: #fe4c50;
  background: white;
  padding: 0 0.5rem;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #fe4c50;
  z-index: 1;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #989898;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #fe4c50;
}

.password-strength {
  margin-top: -1rem;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  width: 0%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
}

.strength-text {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  color: #64748b;
}

.auth-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #64748b;
  cursor: pointer;
  position: relative;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 0;
  height: 0;
}

.checkmark {
  width: 18px;
  height: 18px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-container input:checked ~ .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

.forgot-link,
.terms-link {
  color: #fe4c50;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.forgot-link:hover,
.terms-link:hover {
  color: #1e1e27;
}

.auth-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 1rem;
  background: #fe4c50;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 0.925rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-family: "Poppins", sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.auth-btn:hover {
  background: #1e1e27;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(254, 76, 80, 0.3);
}

.auth-btn:active {
  transform: translateY(0);
}

.btn-icon {
  transition: transform 0.3s ease;
}

.auth-btn:hover .btn-icon {
  transform: translateX(3px);
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
}

.auth-divider::before,
.auth-divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background: #e2e8f0;
}

.auth-divider span {
  padding: 0 1rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.875rem;
  background: white;
  border: 2px solid #fde0db;
  border-radius: 12px;
  font-size: 0.925rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #1e1e27;
  font-family: "Poppins", sans-serif;
}

.social-btn:hover {
  border-color: #fe4c50;
  box-shadow: 0 4px 12px rgba(254, 76, 80, 0.15);
  color: #fe4c50;
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  color: #989898;
  font-size: 0.925rem;
}

.auth-switch a {
  color: #fe4c50;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-switch a:hover {
  color: #1e1e27;
}

/* Background Animation */
.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.background-shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: -2s;
}

.shape-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  right: 10%;
  animation-delay: -4s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: -1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

/* ==================== DASHBOARD STYLES ==================== */
.dashboard-page {
  background: #f8fafc;
  min-height: 100vh;
}

.navbar {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  background: #f1f5f9;
  border-radius: 12px;
  padding: 0.5rem;
  width: 400px;
}

.search-icon {
  color: #64748b;
  margin-right: 0.75rem;
}

.search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.925rem;
}

.search-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-btn:hover {
  background: #5a67d8;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.3s ease;
  position: relative;
}

.nav-item:hover {
  background: #f1f5f9;
}

.nav-label {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-dropdown {
  position: relative;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s ease;
}

.profile-avatar:hover {
  background: #5a67d8;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  min-width: 200px;
  display: none;
  z-index: 1000;
}

.dropdown-menu.show {
  display: block;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-menu a {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #374151;
  transition: background 0.3s ease;
}

.dropdown-menu a:hover {
  background: #f1f5f9;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 3rem;
  margin-bottom: 3rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hero-content {
  flex: 1;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-btn {
  background: white;
  color: #667eea;
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.hero-btn:hover {
  transform: translateY(-2px);
}

.hero-image {
  flex: 0.5;
  display: flex;
  justify-content: center;
}

.hero-graphic {
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1a202c;
}

.categories-section {
  margin-bottom: 3rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
}

.category-card {
  background: white;
  padding: 2rem 1rem;
  border-radius: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.category-card i {
  font-size: 2.5rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.category-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.filters {
  display: flex;
  align-items: center;
  gap: 1rem;
}

#sort-select {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  font-size: 0.925rem;
  cursor: pointer;
}

.view-toggle {
  display: flex;
  gap: 0.25rem;
}

.view-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 200px;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #94a3b8;
}

.product-info {
  padding: 1.5rem;
}

.product-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.product-description {
  color: #64748b;
  font-size: 0.925rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 1rem;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-primary {
  flex: 1;
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-primary:hover {
  background: #5a67d8;
}

.btn-secondary {
  background: #f1f5f9;
  color: #374151;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-secondary:hover {
  background: #e2e8f0;
}

.loading-spinner {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.loading-spinner i {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.cart-sidebar.open {
  right: 0;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.cart-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.close-cart {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #64748b;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.empty-cart {
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
}

.empty-cart i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.cart-footer {
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.cart-total {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.checkout-btn {
  width: 100%;
  background: #10b981;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background 0.3s ease;
}

.checkout-btn:hover {
  background: #059669;
}

/* Modal */
.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  align-items: center;
  justify-content: center;
}

.modal-overlay.show {
  display: flex;
}

.modal-content {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .search-bar {
    width: 100%;
  }

  .hero-section {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .products-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .cart-sidebar {
    width: 100%;
    right: -100%;
  }

  .auth-container {
    padding: 1rem;
  }

  .auth-card {
    padding: 2rem;
  }
}

/* ==================== ADDITIONAL E-COMMERCE STYLES ==================== */
.product-rating {
  color: #fbbf24;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.product-rating span {
  color: #64748b;
  margin-left: 0.5rem;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.cart-item-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart-item-image {
  font-size: 1.5rem;
}

.cart-item-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cart-item-controls button {
  width: 30px;
  height: 30px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-item-controls button:hover {
  background: #f1f5f9;
}

.product-modal-content {
  display: flex;
  gap: 2rem;
  padding: 2rem;
}

.product-modal-image {
  font-size: 8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 12px;
  width: 200px;
  height: 200px;
  flex-shrink: 0;
}

.product-modal-info {
  flex: 1;
}

.product-modal-info h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1a202c;
}

.product-modal-rating {
  color: #fbbf24;
  margin-bottom: 1rem;
}

.product-modal-description {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.product-modal-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 2rem;
}

.product-modal-actions {
  display: flex;
  gap: 1rem;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.products-list .product-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
}

.products-list .product-image {
  width: 100px;
  height: 100px;
  margin-right: 2rem;
  font-size: 2rem;
}

.products-list .product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.products-list .product-actions {
  margin-left: 2rem;
}

/* Additional responsive improvements */
@media (max-width: 480px) {
  .product-modal-content {
    flex-direction: column;
    padding: 1rem;
  }

  .product-modal-image {
    width: 100%;
    height: 150px;
    font-size: 4rem;
  }

  .cart-item {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .hero-title {
    font-size: 1.5rem;
  }
}
